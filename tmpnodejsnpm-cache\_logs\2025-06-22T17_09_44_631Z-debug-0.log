0 verbose cli D:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.0
2 info using node@v20.11.1
3 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\npmrc
4 silly config load:file:C:\laragon\www\PolyFlix\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm run dev
8 verbose argv "run" "dev"
9 verbose logfile logs-max:10 dir:C:\laragon\www\PolyFlix\tmpnodejsnpm-cache\_logs\2025-06-22T17_09_44_631Z-
10 verbose logfile C:\laragon\www\PolyFlix\tmpnodejsnpm-cache\_logs\2025-06-22T17_09_44_631Z-debug-0.log
11 silly logfile done cleaning log files
