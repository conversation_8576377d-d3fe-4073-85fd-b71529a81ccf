<?php $__env->startSection('title', '<PERSON><PERSON>ản lý liên hệ'); ?>
<?php $__env->startSection('page-title', '<PERSON>h sách liên hệ'); ?>
<?php $__env->startSection('breadcrumb', 'Danh sách liên hệ'); ?>

<?php $__env->startSection('styles'); ?>
    <style>
        .card {
            border-radius: 10px;
        }

        .table th,
        .table td {
            vertical-align: middle;
        }

        .badge {
            font-size: 0.9em;
            padding: 0.5em 1em;
        }

        .pagination {
            justify-content: end;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <!-- Thống kê tổng quan -->
        <div class="row mb-4">
            <div class="col-sm-6 col-xl-3">
                <div class="card text-white bg-primary shadow-sm">
                    <div class="card-body">
                        <div class="fs-4 fw-semibold"><?php echo e($stats['total']); ?></div>
                        <div>Tổng số liên hệ</div>
                        <div class="progress progress-white progress-thin my-2">
                            <div class="progress-bar" role="progressbar" style="width: 100%"></div>
                        </div>
                        <small class="text-white">Tất cả liên hệ trong hệ thống</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-xl-3">
                <div class="card text-white bg-warning shadow-sm">
                    <div class="card-body">
                        <div class="fs-4 fw-semibold"><?php echo e($stats['pending']); ?></div>
                        <div>Chưa xử lý</div>
                        <div class="progress progress-white progress-thin my-2">
                            <div class="progress-bar" role="progressbar"
                                style="width: <?php echo e($stats['total'] > 0 ? ($stats['pending'] / $stats['total'] * 100) : 0); ?>%">
                            </div>
                        </div>
                        <small class="text-white">Liên hệ đang chờ xử lý</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-xl-3">
                <div class="card text-white bg-success shadow-sm">
                    <div class="card-body">
                        <div class="fs-4 fw-semibold"><?php echo e($stats['completed']); ?></div>
                        <div>Đã xử lý</div>
                        <div class="progress progress-white progress-thin my-2">
                            <div class="progress-bar" role="progressbar"
                                style="width: <?php echo e($stats['total'] > 0 ? ($stats['completed'] / $stats['total'] * 100) : 0); ?>%">
                            </div>
                        </div>
                        <small class="text-white">Liên hệ đã được xử lý</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-xl-3">
                <div class="card text-white bg-danger shadow-sm">
                    <div class="card-body">
                        <div class="fs-4 fw-semibold"><?php echo e($stats['high_priority']); ?></div>
                        <div>Ưu tiên cao</div>
                        <div class="progress progress-white progress-thin my-2">
                            <div class="progress-bar" role="progressbar"
                                style="width: <?php echo e($stats['total'] > 0 ? ($stats['high_priority'] / $stats['total'] * 100) : 0); ?>%">
                            </div>
                        </div>
                        <small class="text-white">Liên hệ cần ưu tiên xử lý</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow-sm border-0 mb-4">
            <div class="card-header d-flex justify-content-between align-items-center bg-primary text-white">
                <strong>Quản lý liên hệ</strong>
            </div>
            <div class="card-body">
                <!-- Bộ lọc -->
                <form action="<?php echo e(route('admin.lien-he.index')); ?>" method="GET" class="row g-3 mb-4">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" placeholder="Tìm kiếm..." name="search"
                                value="<?php echo e($search ?? ''); ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">-- Trạng thái --</option>
                            <option value="1" <?php echo e(isset($status) && $status == '1' ? 'selected' : ''); ?>>Đã xử lý</option>
                            <option value="0" <?php echo e(isset($status) && $status == '0' ? 'selected' : ''); ?>>Chưa xử lý</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="priority">
                            <option value="">-- Ưu tiên --</option>
                            <option value="cao" <?php echo e(isset($priority) && $priority == 'cao' ? 'selected' : ''); ?>>Cao</option>
                            <option value="binh_thuong" <?php echo e(isset($priority) && $priority == 'binh_thuong' ? 'selected' : ''); ?>>Bình thường</option>
                            <option value="thap" <?php echo e(isset($priority) && $priority == 'thap' ? 'selected' : ''); ?>>Thấp</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="category">
                            <option value="">-- Phân loại --</option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($cat); ?>" <?php echo e(isset($category) && $category == $cat ? 'selected' : ''); ?>>
                                    <?php echo e($cat); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100"><i class="fas fa-filter me-1"></i> Lọc</button>
                    </div>
                </form>

                <!-- Bảng dữ liệu -->
                <div class="table-responsive">
                    <table class="table table-hover table-bordered align-middle">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Tên</th>
                                <th>Email</th>
                                <th>Số điện thoại</th>
                                <th>Trạng thái</th>
                                <th>Ưu tiên</th>
                                <th>Ngày tạo</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $lienHes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lienHe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($lienHe->id); ?></td>
                                    <td>
                                        <a href="<?php echo e(route('admin.lien-he.show', $lienHe->id)); ?>"><?php echo e($lienHe->ten); ?></a>
                                    </td>
                                    <td><?php echo e($lienHe->email); ?></td>
                                    <td><?php echo e($lienHe->so_dien_thoai); ?></td>
                                    <td>
                                        <span class="badge <?php echo e($lienHe->trang_thai ? 'bg-success' : 'bg-warning'); ?>">
                                            <?php echo e($lienHe->trang_thai ? 'Đã xử lý' : 'Chưa xử lý'); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span
                                            class="badge <?php echo e($lienHe->muc_do_uu_tien == 'cao' ? 'bg-danger' : ($lienHe->muc_do_uu_tien == 'thap' ? 'bg-info' : 'bg-secondary')); ?>">
                                            <?php echo e($lienHe->muc_do_uu_tien == 'cao' ? 'Cao' : ($lienHe->muc_do_uu_tien == 'thap' ? 'Thấp' : 'Bình thường')); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e($lienHe->formatted_create_at); ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?php echo e(route('admin.lien-he.show', $lienHe->id)); ?>"
                                                class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form action="<?php echo e(route('admin.lien-he.update', $lienHe->id)); ?>" method="POST"
                                                class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PUT'); ?>
                                                <input type="hidden" name="trang_thai"
                                                    value="<?php echo e($lienHe->trang_thai ? '0' : '1'); ?>">
                                                <input type="hidden" name="ghi_chu_noi_bo"
                                                    value="<?php echo e($lienHe->ghi_chu_noi_bo); ?>">
                                                <button type="submit" class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-check-circle"></i>
                                                </button>
                                            </form>
                                            <form action="<?php echo e(route('admin.lien-he.destroy', $lienHe->id)); ?>" method="POST"
                                                class="d-inline"
                                                onsubmit="return confirm('Bạn có chắc chắn muốn xóa liên hệ này?');">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-3">Không có dữ liệu</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <small class="text-muted">Hiển thị <?php echo e($lienHes->count()); ?> trong tổng số <?php echo e($lienHes->total()); ?>

                            liên hệ</small>
                    </div>
                    <div>
                        <?php echo e($lienHes->links('pagination::bootstrap-5')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PolyFlix\resources\views/admin/lien-he/index.blade.php ENDPATH**/ ?>