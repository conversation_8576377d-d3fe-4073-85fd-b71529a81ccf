    

    <?php $__env->startSection('title', 'Quản lý Đặt vé'); ?>
    <?php $__env->startSection('page-title', 'Danh sách Đặt vé'); ?>
    <?php $__env->startSection('breadcrumb', 'Danh sách Đặt vé'); ?>

    <?php $__env->startSection('content'); ?>

        <?php echo app('Illuminate\Foundation\Vite')('resources/js/dat-ve.js'); ?>

        <!-- Modal quét mã -->
        <div class="modal fade" id="scannerModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Quét mã vạch</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <div id="barcode-scanner" style="width:100%; height:500px; background:rgb(255, 252, 252);"></div>
                        <p><strong>Kết quả:</strong> <span id="scan-result">Chưa quét</span></p>
                    </div>
                    <div class="modal-footer">
                        <button id="restartScan" type="button" class="btn btn-primary">Quét lại </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid px-4">
            <div class="card shadow rounded-4 border-0 mb-4">
                <div class="card-header bg-gradient bg-primary text-white rounded-top-4">
                    <h4 class="mb-0 py-2"><i class="fas fa-ticket-alt me-2"></i>Danh sách Đặt vé</h4>
                </div>

                <div class="card-body py-4">


                    <!-- Bộ lọc -->
                    <form ... class="filter-form d-flex flex-wrap align-items-end gap-4 mb-4">
                        <form method="GET" action="<?php echo e(route('admin.dat-ves.index')); ?>"
                            class="filter-form d-flex flex-wrap align-items-end gap-4">
                            
                            <div class="form-group">
                                <label class="form-label fw-semibold mb-1">Chi nhánh</label>
                                <select name="chi_nhanh" class="form-select" style="min-width: 220px"
                                    onchange="this.form.submit()">
                                    <option value="">-- Tất cả chi nhánh --</option>
                                    <?php $__currentLoopData = $chiNhanhs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cn): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($cn->id); ?>"
                                            <?php echo e(request('chi_nhanh') == $cn->id ? 'selected' : ''); ?>>
                                            <?php echo e($cn->ten_chi_nhanh); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            
                            <div class="form-group">
                                <label class="form-label fw-semibold mb-1">Rạp</label>
                                <select name="rap" class="form-select" style="min-width: 220px"
                                    onchange="this.form.submit()">
                                    <option value="">-- Tất cả rạp --</option>
                                    <?php $__currentLoopData = $chiNhanhs->flatMap->rapPhims; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rap): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($rap->id); ?>"
                                            <?php echo e(request('rap') == $rap->id ? 'selected' : ''); ?>>
                                            <?php echo e($rap->ten_rap); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            
                            <div class="form-group">
                                <label class="form-label fw-semibold mb-1">Phim</label>
                                <select name="phim" class="form-select" style="min-width: 220px"
                                    onchange="this.form.submit()">
                                    <option value="">-- Tất cả phim --</option>
                                    <?php $__currentLoopData = $dsPhim; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $phim): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($phim->id); ?>"
                                            <?php echo e(request('phim') == $phim->id ? 'selected' : ''); ?>>
                                            <?php echo e($phim->ten_phim); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            
                            <div class="form-group d-flex align-items-end">
                                <button type="submit" class="btn d-flex align-items-center gap-1 px-4 py-2 text-white"
                                    style="background-color: #6c4efc; border-radius: 0.5rem;">
                                    <i class="bi bi-funnel-fill"></i> Lọc
                                </button>
                            </div>

                            
                            <div class="form-group d-flex align-items-end">
                                <a href="<?php echo e(route('admin.dat-ves.index')); ?>"
                                    class="btn btn-outline-secondary d-flex align-items-center gap-1 px-4 py-2"
                                    style="border-radius: 0.5rem;">
                                    <i class="bi bi-arrow-clockwise"></i> Xóa bộ lọc
                                </a>
                            </div>
                        </form>



                        <!-- Bảng dữ liệu -->
                        <?php if($datVes->count()): ?>
                            <div class="table-responsive">
                                <table class="table table-hover align-middle text-center">
                                    <thead class="table-light">
                                        <tr>
                                            <th scope="col">STT</th>
                                            <th>Mã vé</th>
                                            <th scope="col"><i class="fas fa-film"></i> Phim</th>
                                            <th scope="col"><i class="far fa-clock"></i> Thời gian đặt</th>
                                            <th scope="col"><i class="fas fa-cogs"></i> Hành động</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $datVes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $datVe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($index + 1); ?></td>
                                                <td><?php echo e($datVe->ma_dat_ve); ?></td>
                                                <td><?php echo e($datVe->suatChieu->phim->ten_phim); ?></td>
                                                <td><?php echo e($datVe->created_at->format('H:i d/m/Y')); ?></td>
                                                <td>
                                                    <a class="btn btn-warning"
                                                        href="<?php echo e(route('admin.dat-ve.show', ['id' => $datVe->id, 'ma_ve' => $datVe->ma_dat_ve])); ?>">
                                                        <i class="fa-solid fa-eye fa-spin-pulse"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info mt-4">
                                <i class="fas fa-info-circle me-1"></i> Không có dữ liệu đặt vé nào.
                            </div>
                        <?php endif; ?>
                </div>
            </div>
        </div>

    <?php $__env->stopSection(); ?>

    

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PolyFlix\resources\views/admin/dat-ve/index.blade.php ENDPATH**/ ?>