<?php $__env->startSection('title', 'Quản lý liên hệ'); ?>
<?php $__env->startSection('page-title', '<PERSON> tiết liên hệ'); ?>
<?php $__env->startSection('breadcrumb', 'Chi tiết liên hệ'); ?>

<?php $__env->startSection('content'); ?>
<div class="body flex-grow-1">
    <div class="container-lg px-4">
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <strong>Chi tiết liên hệ #<?php echo e($lienHe->id); ?></strong>
                    <div>
                        <a href="<?php echo e(route('admin.lien-he.index')); ?>" class="btn btn-outline-primary btn-sm me-2">
                            <svg class="icon me-2">
                                <use xlink:href="<?php echo e(asset('dist/vendors/@coreui/icons/svg/free.svg#cil-arrow-left')); ?>"></use>
                            </svg>Quay lại
                        </a>
                        <a href="<?php echo e(route('admin.lien-he.show', $lienHe->id)); ?>" class="btn btn-outline-info btn-sm">
                            <svg class="icon me-2">
                                <use xlink:href="<?php echo e(asset('dist/vendors/@coreui/icons/svg/free.svg#cil-info')); ?>"></use>
                            </svg>Chi tiết
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Thông tin liên hệ -->
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <strong>Thông tin liên hệ</strong>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-striped">
                                            <tr>
                                                <th style="width: 150px">ID:</th>
                                                <td><?php echo e($lienHe->id); ?></td>
                                            </tr>
                                            <tr>
                                                <th>Tên:</th>
                                                <td><?php echo e($lienHe->ten); ?></td>
                                            </tr>
                                            <tr>
                                                <th>Email:</th>
                                                <td><?php echo e($lienHe->email); ?></td>
                                            </tr>
                                            <tr>
                                                <th>Số điện thoại:</th>
                                                <td><?php echo e($lienHe->so_dien_thoai); ?></td>
                                            </tr>
                                            <tr>
                                                <th>Trạng thái:</th>
                                                <td>
                                                    <?php if($lienHe->trang_thai): ?>
                                                        <span class="badge bg-success">Đã xử lý</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">Chưa xử lý</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Đã phản hồi:</th>
                                                <td>
                                                    <?php if($lienHe->da_phan_hoi): ?>
                                                        <span class="badge bg-success">Đã phản hồi</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Chưa phản hồi</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-striped">
                                            <tr>
                                                <th style="width: 150px">Mức độ ưu tiên:</th>
                                                <td>
                                                    <span class="badge <?php echo e($lienHe->muc_do_uu_tien == 'cao' ? 'bg-danger' : ($lienHe->muc_do_uu_tien == 'thap' ? 'bg-info' : 'bg-warning')); ?>">
                                                        <?php echo e($lienHe->muc_do_uu_tien == 'cao' ? 'Cao' : ($lienHe->muc_do_uu_tien == 'thap' ? 'Thấp' : 'Bình thường')); ?>

                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Nguồn gốc:</th>
                                                <td><?php echo e($lienHe->nguon_goc ?? 'Không có'); ?></td>
                                            </tr>
                                            <tr>
                                                <th>Phân loại:</th>
                                                <td><?php echo e($lienHe->phan_loai ?? 'Không có'); ?></td>
                                            </tr>
                                            <tr>
                                                <th>Người phụ trách:</th>
                                                <td><?php echo e($lienHe->nguoi_phu_trach ?? 'Chưa phân công'); ?></td>
                                            </tr>
                                            <tr>
                                                <th>Ngày tạo:</th>
                                                <td><?php echo e($lienHe->formatted_create_at); ?></td>
                                            </tr>
                                            <tr>
                                                <th>Cập nhật lần cuối:</th>
                                                <td><?php echo e($lienHe->formatted_update_at); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6>Nội dung liên hệ:</h6>
                                        <div class="p-3 bg-light rounded">
                                            <?php echo e($lienHe->noi_dung); ?>

                                        </div>
                                    </div>
                                </div>

                                <?php if($lienHe->ghi_chu_noi_bo): ?>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6>Ghi chú nội bộ:</h6>
                                        <div class="p-3 bg-light rounded">
                                            <?php echo e($lienHe->ghi_chu_noi_bo); ?>

                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Lịch sử hoạt động -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <strong>Lịch sử hoạt động</strong>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover table-striped">
                                        <thead>
                                            <tr>
                                                <th>Thời gian</th>
                                                <th>Hành động</th>
                                                <th>Mô tả</th>
                                                <th>Người thực hiện</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__empty_1 = true; $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <tr>
                                                    <td><?php echo e($activity->formatted_created_at); ?></td>
                                                    <td>
                                                        <?php switch($activity->hanh_dong):
                                                            case ('create'): ?>
                                                                <span class="badge bg-success">Tạo mới</span>
                                                                <?php break; ?>
                                                            <?php case ('update'): ?>
                                                                <span class="badge bg-primary">Cập nhật</span>
                                                                <?php break; ?>
                                                            <?php case ('delete'): ?>
                                                                <span class="badge bg-danger">Xóa</span>
                                                                <?php break; ?>
                                                            <?php case ('add_note'): ?>
                                                                <span class="badge bg-info">Thêm ghi chú</span>
                                                                <?php break; ?>
                                                            <?php case ('update_status'): ?>
                                                                <span class="badge bg-warning">Cập nhật trạng thái</span>
                                                                <?php break; ?>
                                                            <?php case ('send_email'): ?>
                                                                <span class="badge bg-success">Gửi email</span>
                                                                <?php break; ?>
                                                            <?php default: ?>
                                                                <span class="badge bg-secondary"><?php echo e($activity->hanh_dong); ?></span>
                                                        <?php endswitch; ?>
                                                    </td>
                                                    <td><?php echo e($activity->mo_ta); ?></td>
                                                    <td><?php echo e($activity->nguoi_thuc_hien); ?></td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">Không có dữ liệu</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-md-4">
                        <!-- Thêm ghi chú -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <strong>Thêm ghi chú</strong>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo e(route('admin.lien-he.add-note', $lienHe->id)); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                    <div class="mb-3">
                                        <label for="noi_dung" class="form-label">Nội dung ghi chú</label>
                                        <textarea class="form-control" id="noi_dung" name="noi_dung" rows="4" required></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <svg class="icon me-2">
                                            <use xlink:href="<?php echo e(asset('dist/vendors/@coreui/icons/svg/free.svg#cil-note-add')); ?>"></use>
                                        </svg>Thêm ghi chú
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Gửi email phản hồi -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <strong>Gửi email phản hồi</strong>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo e(route('admin.lien-he.send-email', $lienHe->id)); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                    <div class="mb-3">
                                        <label for="subject" class="form-label">Tiêu đề</label>
                                        <input type="text" class="form-control" id="subject" name="subject" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="message" class="form-label">Nội dung</label>
                                        <textarea class="form-control" id="message" name="message" rows="6" required></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-success w-100">
                                        <svg class="icon me-2">
                                            <use xlink:href="<?php echo e(asset('dist/vendors/@coreui/icons/svg/free.svg#cil-send')); ?>"></use>
                                        </svg>Gửi email
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Danh sách ghi chú -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <strong>Ghi chú</strong>
                            </div>
                            <div class="card-body">
                                <?php $__empty_1 = true; $__currentLoopData = $notes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $note): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <div class="note-item mb-3 p-3 border rounded">
                                        <div class="d-flex justify-content-between mb-2">
                                            <small class="text-muted"><?php echo e($note->formatted_created_at); ?></small>
                                            <small class="text-muted"><?php echo e($note->nguoi_tao); ?></small>
                                        </div>
                                        <p class="mb-0"><?php echo e($note->noi_dung); ?></p>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <p class="text-center text-muted">Chưa có ghi chú nào</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Thao tác -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <strong>Thao tác</strong>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <form action="<?php echo e(route('admin.lien-he.update', $lienHe->id)); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('PUT'); ?>
                                        <button type="submit" class="btn btn-<?php echo e($lienHe->trang_thai ? 'warning' : 'success'); ?> w-100">
                                            <svg class="icon me-2">
                                                <use xlink:href="<?php echo e(asset('dist/vendors/@coreui/icons/svg/free.svg#cil-check-circle')); ?>"></use>
                                            </svg><?php echo e($lienHe->trang_thai ? 'Đánh dấu chưa xử lý' : 'Đánh dấu đã xử lý'); ?>

                                        </button>
                                        <input type="hidden" name="trang_thai" value="<?php echo e($lienHe->trang_thai ? '0' : '1'); ?>">
                                        <input type="hidden" name="ghi_chu_noi_bo" value="<?php echo e($lienHe->ghi_chu_noi_bo); ?>">
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PolyFlix\resources\views/admin/lien-he/show.blade.php ENDPATH**/ ?>