0 verbose cli D:\Program Files\nodejs\node.exe D:\Program Files\nodejs\node_modules\npm\bin\npm-cli.js
1 info using npm@10.2.4
2 info using node@v20.11.1
3 timing npm:load:whichnode Completed in 4ms
4 timing config:load:defaults Completed in 3ms
5 timing config:load:file:D:\Program Files\nodejs\node_modules\npm\npmrc Completed in 5ms
6 timing config:load:builtin Completed in 5ms
7 timing config:load:cli Completed in 3ms
8 timing config:load:env Completed in 1ms
9 timing config:load:project Completed in 0ms
10 timing config:load:file:C:\Users\<USER>\.npmrc Completed in 0ms
11 timing config:load:user Completed in 0ms
12 timing config:load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc Completed in 0ms
13 timing config:load:global Completed in 1ms
14 timing config:load:setEnvs Completed in 0ms
15 timing config:load Completed in 14ms
16 timing npm:load:configload Completed in 14ms
17 timing config:load:flatten Completed in 2ms
18 timing npm:load:mkdirpcache Completed in 1ms
19 timing npm:load:mkdirplogs Completed in 0ms
20 verbose title npm prefix
21 verbose argv "prefix" "--global"
22 timing npm:load:setTitle Completed in 1ms
23 timing npm:load:display Completed in 0ms
24 verbose logfile logs-max:10 dir:C:\laragon\www\PolyFlix\tmpnodejsnpm-cache\_logs\2025-06-22T18_06_10_498Z-
25 verbose logfile C:\laragon\www\PolyFlix\tmpnodejsnpm-cache\_logs\2025-06-22T18_06_10_498Z-debug-0.log
26 timing npm:load:logFile Completed in 12ms
27 timing npm:load:timers Completed in 0ms
28 timing npm:load:configScope Completed in 0ms
29 timing npm:load Completed in 68ms
30 timing command:prefix Completed in 2ms
31 verbose exit 0
32 timing npm Completed in 159ms
33 info ok
