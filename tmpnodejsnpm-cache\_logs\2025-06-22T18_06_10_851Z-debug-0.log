0 verbose cli D:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.0
2 info using node@v20.11.1
3 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\npmrc
4 silly config load:file:C:\laragon\www\PolyFlix\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm run build
8 verbose argv "run" "build"
9 verbose logfile logs-max:10 dir:C:\laragon\www\PolyFlix\tmpnodejsnpm-cache\_logs\2025-06-22T18_06_10_851Z-
10 verbose logfile C:\laragon\www\PolyFlix\tmpnodejsnpm-cache\_logs\2025-06-22T18_06_10_851Z-debug-0.log
11 silly logfile start cleaning logs, removing 2 files
12 silly logfile done cleaning log files
13 verbose cwd C:\laragon\www\PolyFlix
14 verbose os Windows_NT 10.0.26100
15 verbose node v20.11.1
16 verbose npm  v10.9.0
17 verbose exit 0
18 info ok
