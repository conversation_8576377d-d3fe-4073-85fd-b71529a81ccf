<?php $__env->startSection('content'); ?>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <?php echo app('Illuminate\Foundation\Vite')('resources/js/login.js'); ?>
    <div class="login-container">
        <div class="tab-main">
            <div class="tab <?php echo e(session('active_tab') != 'register' ? 'active' : ''); ?>" data-tab="login"
                onclick="showTab('login')">ĐĂNG NHẬP</div>
            <div class="tab <?php echo e(session('active_tab') == 'register' ? 'active' : ''); ?>" data-tab="register"
                onclick="showTab('register')">ĐĂNG KÝ</div>
        </div>

        <?php if(session('success')): ?>
            <script>
                document.addEventListener("DOMContentLoaded", function() {
                    Swal.fire({
                        title: '<?php echo e(session('success')); ?>',
                        confirmButtonText: 'OK',
                        customClass: {
                            popup: 'custom-popup',
                            confirmButton: 'custom-confirm-button'
                        },
                        buttonsStyling: false
                    });
                });
            </script>
        <?php endif; ?>

        <?php if($errors->any()): ?>
            <script>
                document.addEventListener("DOMContentLoaded", function() {
                    Swal.fire({
                        html: `<?php echo implode('<br>', $errors->all()); ?>`,
                        confirmButtonText: 'OK',
                        customClass: {
                            popup: 'custom-popup',
                            confirmButton: 'custom-confirm-button'
                        },
                        buttonsStyling: false
                    });
                });
            </script>
        <?php endif; ?>

        <div class="tab-content">
            <form action="<?php echo e(route('login')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="tab-pane <?php echo e(session('active_tab') != 'register' ? 'show slide-down' : ''); ?>" id="login">
                    <!-- Đăng nhập -->
                    <div class="form-group">
                        <label for="login_email">Email</label>
                        <input type="email" id="login_email" name="email" class="form-control" autocomplete="email"
                            placeholder="Nhập email" value="<?php echo e(old('email')); ?>">
                    </div>

                    <div class="form-group">
                        <label for="login_pass">Mật khẩu</label>
                        <div class="password-wrapper">
                            <input type="password" id="login_pass" name="password" autocomplete="current-password"
                                class="form-control" placeholder="Nhập mật khẩu">
                            <span class="toggle-password" data-target="#login_pass">
                                <i class="fa-solid fa-eye-slash" style="color: #B197FC;"></i>
                            </span>
                        </div>
                    </div>

                    <div class="remember-pass">
                        <label class="custom-checkbox">
                            <input type="checkbox" name="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                            <span class="checkmark"></span>
                            <p>Ghi nhớ đăng nhập</p>
                        </label>
                    </div>

                    <a class="forgot-pass" href="<?php echo e(route('forgot-form')); ?>">Quên mật khẩu?</a>

                    <button type="submit">Đăng nhập</button>
                </div>
            </form>

            <form action="<?php echo e(route('register')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="tab-pane <?php echo e(session('active_tab') == 'register' ? 'show slide-up' : ''); ?>" id="register">
                    <div class="form-group">
                        <label for="name">Họ và tên</label>
                        <input type="text" id="name" name="name" class="form-control"
                            value="<?php echo e(old('name')); ?>" placeholder="Nhập họ và tên">
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <small class="text-danger"><?php echo e($message); ?></small>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="form-group">
                        <label for="dob">Ngày sinh</label>
                        <input type="date" id="dob" name="dob" class="form-control"
                            value="<?php echo e(old('dob')); ?>">
                        <?php $__errorArgs = ['dob'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <small class="text-danger"><?php echo e($message); ?></small>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="form-group">
                        <label for="phone">Số điện thoại</label>
                        <input type="tel" id="phone" name="phone" class="form-control"
                            value="<?php echo e(old('phone')); ?>" placeholder="Nhập số điện thoại">
                        <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <small class="text-danger"><?php echo e($message); ?></small>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="form-group">
                        <label for="register_email">Email</label>
                        <input type="email" id="register_email" name="email" class="form-control"
                            value="<?php echo e(old('email')); ?>" placeholder="Nhập email">
                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <small class="text-danger"><?php echo e($message); ?></small>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="form-group">
                        <label for="register_password">Mật khẩu</label>
                        <div class="password-wrapper">
                            <input type="password" id="register_password" name="password" class="form-control"
                                placeholder="Nhập mật khẩu">
                            <span class="toggle-password" data-target="#register_password">
                                <i class="fa-solid fa-eye-slash" style="color: #B197FC;"></i>
                            </span>
                        </div>
                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <small class="text-danger"><?php echo e($message); ?></small>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">Xác nhận mật khẩu</label>
                        <div class="password-wrapper">
                            <input type="password" id="confirm_password" name="password_confirmation" class="form-control"
                                placeholder="Nhập lại mật khẩu" value="<?php echo e(old('password_confirmation')); ?>">
                            <span class="toggle-password" data-target="#confirm_password">
                                <i class="fa-solid fa-eye-slash" style="color: #B197FC;"></i>
                            </span>
                        </div>
                    </div>

                    <button type="submit">Đăng ký</button>
                </div>
            </form>


            <?php if(session('active_tab')): ?>
                <script>
                    document.addEventListener("DOMContentLoaded", function() {
                        showTab("<?php echo e(session('active_tab')); ?>");
                    });
                </script>
            <?php endif; ?>

        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.client', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PolyFlix\resources\views/auth/login.blade.php ENDPATH**/ ?>