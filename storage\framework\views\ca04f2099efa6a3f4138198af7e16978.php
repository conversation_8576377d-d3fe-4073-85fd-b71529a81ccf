<?php $__env->startSection('content'); ?>
    <link href="https://fonts.googleapis.com/css2?family=Anton&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
        integrity="sha512-dYkA5Kj8SGrWJQ2r7S4JblmQo2+3ZJfzv+y5eA6TeK4kD4i2yHMyhzTKoH9yKxKdRYg3C1f58TbzOdKJejO3dg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <?php echo app('Illuminate\Foundation\Vite')('resources/js/trang-chu.js'); ?>

    
        
<!-- Swiper CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />

<!-- Banner Slider -->
<div class="swiper banner-slider" style="max-width: 1200px; width: 100%; height: 500px; margin: 0 auto 20px auto;">
    <div class="swiper-wrapper">
        <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="swiper-slide">
                <img src="<?php echo e(asset($banner->hinh_anh)); ?>" alt="Banner <?php echo e($banner->id); ?>" 
                     style="width: 100%; height: 100%; object-fit: cover;">
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <div class="swiper-pagination"></div>
    <div class="swiper-button-next"></div>
    <div class="swiper-button-prev"></div>
</div>


<!-- Swiper JS -->
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

<!-- Swiper Init -->
<script>
    const swiper = new Swiper('.banner-slider', {
        loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
    });
</script>


    <div class="booking-fast">
        <div class="btn">
            <span>ĐẶT VÉ NHANH</span>
        </div>
        <div class="select">
            <select required name="select-chi-nhanh" id="select-chi-nhanh" class="movie-select">
                <option value="" disabled selected>1-Chọn rạp</option>
            </select>
            <select required name="select-phim" id="select-phim" class="movie-select" disabled>
                <option disabled selected value="">2-Chọn phim</option>
            </select>
            <select required name="select-date" id="select-date" class="movie-select" disabled>
                <option disabled selected value="">3-Chọn ngày</option>
            </select>
            <select required name="select-suat" id="select-suat" class="movie-select" disabled>
                <option disabled selected value="">4-Chọn suất</option>
            </select>
            <button id="btn-dat-ngay" disabled>Đặt ngay</button>
        </div>

        <!-- Loading indicator -->
        <div id="booking-loading" class="booking-loading" style="display: none;">
            <div class="spinner"></div>
            <span>Đang tải...</span>
        </div>
    </div>

    <div class="menu">
        <button type="button"></button>
        <p class="movie">PHIM</p>
        <div class="list">
            <p>Đang chiếu</p>
            <p>Sắp chiếu</p>
        </div>
    </div>

    <div class="list-movie">
        <div class="movie">
            <div class="img-wrapper">
                <img src="https://cdn.galaxycine.vn/media/2025/2/17/bi-kip-luyen-rong-500_1739776695143.jpg" alt="">
                <div class="overlay">
                    <button class="btn buy"><i class="fa-solid fa-ticket"></i> Mua vé</button>
                    <button class="btn trailer"><i class="fa-solid fa-video"></i> Trailer</button>
                </div>
            </div>
            <p>Bí Kíp Luyện Rồng</p>
        </div>
        <div class="movie">
            <div class="img-wrapper">
                <img src="https://cdn.galaxycine.vn/media/2025/2/17/bi-kip-luyen-rong-500_1739776695143.jpg" alt="">
                <div class="overlay">
                    <button class="btn buy"><i class="fa-solid fa-ticket"></i> Mua vé</button>
                    <button class="btn trailer"><i class="fa-solid fa-video"></i> Trailer</button>
                </div>
            </div>
            <p>Bí Kíp Luyện Rồng</p>
        </div>
        <div class="movie">
            <div class="img-wrapper">
                <img src="https://cdn.galaxycine.vn/media/2025/2/17/bi-kip-luyen-rong-500_1739776695143.jpg" alt="">
                <div class="overlay">
                    <button class="btn buy"><i class="fa-solid fa-ticket"></i> Mua vé</button>
                    <button class="btn trailer"><i class="fa-solid fa-video"></i> Trailer</button>
                </div>
            </div>
            <p>Bí Kíp Luyện Rồng</p>
        </div>
        <div class="movie">
            <div class="img-wrapper">
                <img src="https://cdn.galaxycine.vn/media/2025/2/17/bi-kip-luyen-rong-500_1739776695143.jpg" alt="">
                <div class="overlay">
                    <button class="btn buy"><i class="fa-solid fa-ticket"></i> Mua vé</button>
                    <button class="btn trailer"><i class="fa-solid fa-video"></i> Trailer</button>
                </div>
            </div>
            <p>Bí Kíp Luyện Rồng</p>
        </div>

    </div>
    <button class="btn-see">XEM THÊM</button>

    <div class="khuyen-mai">
        <p>KHUYẾN MÃI</p>
        <div class="img">
            <img width="350px" src="<?php echo e(asset('khuyen-mai/c_student.png')); ?>" alt="">
            <img width="350px" src="<?php echo e(asset('khuyen-mai/C_TEN.png')); ?>" alt="">
            <img width="350px" src="<?php echo e(asset('khuyen-mai/monday_1_.jpg')); ?>" alt="">
        </div>
    </div>
    
    <a href="<?php echo e(route('khuyen-mai.index')); ?>">
    <button class="btn-km">TẤT CẢ ƯU ĐÃI</button>
</a>
    <div class="new">
        <button type="button"></button>
        <p>GÓC ĐIỆN ẢNH</p>
        <div class="list">
            <p class="tab-item active" data-tab="binhluan">Bình luận phim</p>
            <p class="tab-item" data-tab="blog">Blog điện ảnh</p>
        </div>
    </div>

    <div id="tab-binhluan" class="tab-content active">
        <div id="slide-container">
            <?php $__currentLoopData = $phims; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $phim): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="slide" style="<?php echo e($index === 0 ? '' : 'display:none;'); ?>">
                    <div class="khung-binh-luan">

                        
                        <div class="poster">
                            <img src="<?php echo e(asset('storage/' . $phim->poster)); ?>" alt="<?php echo e($phim->ten_phim); ?>">
                        </div>

                        
                        <div class="binh-luan">
                            <h4 class="ten-phim"><?php echo e($phim->ten_phim); ?></h4>
                            <?php $__empty_1 = true; $__currentLoopData = $phim->comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $binhLuan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <?php
                                    $userRating = $ratings->first(function ($r) use ($binhLuan) {
                                        return $r->user_id === $binhLuan->user_id && $r->phim_id === $binhLuan->phim_id;
                                    });
                                ?>
                                <div class="binh-luan-item">
                                    <div class="avatar">
                                        <img src="<?php echo e($binhLuan->user && $binhLuan->user->avatar
                                            ? asset('storage/' . $binhLuan->user->avatar)
                                            : asset('logo/user.jpg')); ?>"
                                            alt="<?php echo e($binhLuan->user->name ?? 'Người dùng'); ?>">
                                    </div>
                                    <div class="noi-dung">
                                        <strong><?php echo e($binhLuan->user->name ?? 'Ẩn danh'); ?></strong>
                                        <?php if($userRating): ?>
                                            <span style="color: orange;">
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <?php echo e($i <= $userRating->rating ? '★' : '☆'); ?>

                                                <?php endfor; ?>
                                            </span>
                                        <?php endif; ?>
                                        <p><?php echo e($binhLuan->content); ?></p>
                                        <small><?php echo e($binhLuan->created_at->format('d/m/Y H:i')); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <p style="color: #ccc;">Chưa có bình luận cho phim này.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <div class="slide-controls">
            <div class="slide-button-group">
                <button class="btn-see1 left" onclick="changeSlide(-1)">‹</button>
                <button class="btn-see1 right" onclick="changeSlide(1)">›</button>
            </div>
        </div>
    </div>

    <div id="tab-blog" class="tab-content">
        <?php if($baiViet && count($baiViet)): ?>
            <div class="tin-tuc-wrapper">
                
                <div class="tin-tuc-noi-bat">
                    <img src="<?php echo e(asset('storage/' . $baiViet[0]->hinh_anh)); ?>" alt="<?php echo e($baiViet[0]->tieu_de); ?>">
                    <a href="<?php echo e(route('show-bai-viet', $baiViet[0]->id)); ?>">
                        <h3><?php echo e($baiViet[0]->tieu_de); ?></h3>
                    </a>
                </div>

                
                <div class="tin-tuc-danh-sach">
                    <?php $__currentLoopData = $baiViet->skip(1); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bv): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="tin-tuc-item">
                            <div class="thumb">
                                <a href="<?php echo e(route('show-bai-viet', $bv->id)); ?>">
                                    <img src="<?php echo e(asset('storage/' . $bv->hinh_anh)); ?>" alt="<?php echo e($bv->tieu_de); ?>">
                                </a>
                            </div>
                            <div class="info">
                                <a href="<?php echo e(route('show-bai-viet', $bv->id)); ?>">
                                    <h4><?php echo e($bv->tieu_de); ?></h4>
                                    <p class="noi-dung"><?php echo e($bv->noi_dung); ?></p>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <a href="<?php echo e(route('client.bai-viet')); ?>">
                    <button class="btn-see">Xem thêm</button>
                </a>
            </div>
        <?php else: ?>
            <p>Chưa có bài viết nào.</p>
        <?php endif; ?>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.style.display = (i === index) ? 'block' : 'none';
            });
        }

        function changeSlide(direction) {
            currentSlide += direction;
            if (currentSlide >= slides.length) currentSlide = 0;
            if (currentSlide < 0) currentSlide = slides.length - 1;
            showSlide(currentSlide);
        }

        setInterval(() => changeSlide(1), 5000);

        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                this.classList.add('active');
                document.getElementById('tab-' + this.dataset.tab).classList.add('active');
            });
        });
    </script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.client', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PolyFlix\resources\views/client/trang-chu.blade.php ENDPATH**/ ?>